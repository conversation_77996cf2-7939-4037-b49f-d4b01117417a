@echo off
echo ========================================
echo  Convertisseur Image vers Texte
echo ========================================
echo.

echo Verification des dependances...
python -c "import flask, cv2, pytesseract, PIL, numpy" 2>nul
if errorlevel 1 (
    echo ERREUR: Dependances manquantes!
    echo Installation des dependances...
    pip install Flask opencv-python pytesseract Pillow numpy
    if errorlevel 1 (
        echo ERREUR: Impossible d'installer les dependances
        pause
        exit /b 1
    )
)

echo Dependances OK!
echo.

echo Verification de Tesseract OCR...
tesseract --version >nul 2>&1
if errorlevel 1 (
    echo ATTENTION: Tesseract OCR n'est pas installe ou pas dans le PATH
    echo L'application fonctionnera mais l'OCR ne sera pas disponible
    echo Consultez install_tesseract.md pour les instructions d'installation
    echo.
    echo Appuyez sur une touche pour continuer quand meme...
    pause >nul
) else (
    echo Tesseract OCR detecte!
)

echo.
echo Demarrage de l'application...
echo L'application sera accessible sur: http://localhost:5000
echo.
echo Appuyez sur Ctrl+C pour arreter l'application
echo.

python app.py
