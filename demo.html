<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démo - Convertisseur Image vers Texte</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #667eea;
            margin-bottom: 30px;
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
        }
        .demo-section h2 {
            color: #333;
            margin-bottom: 15px;
        }
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .feature i {
            color: #667eea;
            margin-right: 10px;
            width: 20px;
        }
        .screenshot {
            text-align: center;
            margin: 20px 0;
        }
        .screenshot img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px 5px;
            transition: transform 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-image"></i> Convertisseur Image vers Texte - Démo</h1>
        
        <div class="demo-section">
            <h2><i class="fas fa-star"></i> Fonctionnalités</h2>
            <div class="feature">
                <i class="fas fa-upload"></i>
                <span>Glisser-déposer ou sélection de fichiers image</span>
            </div>
            <div class="feature">
                <i class="fas fa-magic"></i>
                <span>Extraction de texte avec OCR avancé (Tesseract)</span>
            </div>
            <div class="feature">
                <i class="fas fa-mobile-alt"></i>
                <span>Interface responsive compatible mobile et desktop</span>
            </div>
            <div class="feature">
                <i class="fas fa-copy"></i>
                <span>Copier, télécharger et analyser le texte extrait</span>
            </div>
            <div class="feature">
                <i class="fas fa-globe"></i>
                <span>Support multi-langues (français, anglais, etc.)</span>
            </div>
            <div class="feature">
                <i class="fas fa-cog"></i>
                <span>Prétraitement d'image automatique pour une meilleure précision</span>
            </div>
        </div>

        <div class="demo-section">
            <h2><i class="fas fa-rocket"></i> Démarrage rapide</h2>
            
            <div class="warning">
                <strong><i class="fas fa-exclamation-triangle"></i> Prérequis :</strong>
                Tesseract OCR doit être installé sur votre système. 
                <a href="install_tesseract.md" target="_blank">Voir les instructions d'installation</a>
            </div>

            <h3>1. Installation des dépendances</h3>
            <div class="code-block">
pip install Flask opencv-python pytesseract Pillow numpy
            </div>

            <h3>2. Lancement de l'application</h3>
            <div class="code-block">
python app.py
            </div>

            <h3>3. Accès à l'interface</h3>
            <div class="code-block">
http://localhost:5000
            </div>
        </div>

        <div class="demo-section">
            <h2><i class="fas fa-file-image"></i> Formats supportés</h2>
            <p>L'application accepte les formats d'image suivants :</p>
            <ul>
                <li><strong>PNG</strong> - Portable Network Graphics</li>
                <li><strong>JPG/JPEG</strong> - Joint Photographic Experts Group</li>
                <li><strong>GIF</strong> - Graphics Interchange Format</li>
                <li><strong>BMP</strong> - Bitmap</li>
                <li><strong>TIFF/TIF</strong> - Tagged Image File Format</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2><i class="fas fa-code"></i> API REST</h2>
            
            <h3>Upload et extraction de texte</h3>
            <div class="code-block">
curl -X POST -F "file=@image.jpg" http://localhost:5000/upload
            </div>

            <h3>Vérification du service</h3>
            <div class="code-block">
curl http://localhost:5000/health
            </div>

            <h3>Réponse JSON</h3>
            <div class="code-block">
{
  "success": true,
  "text": "Texte extrait de l'image..."
}
            </div>
        </div>

        <div class="demo-section">
            <h2><i class="fas fa-tools"></i> Dépannage</h2>
            
            <h3>Erreur "Tesseract not found"</h3>
            <ul>
                <li>Vérifiez que Tesseract est installé</li>
                <li>Ajoutez Tesseract au PATH système</li>
                <li>Redémarrez votre terminal/IDE</li>
            </ul>

            <h3>Aucun texte détecté</h3>
            <ul>
                <li>Vérifiez la qualité de l'image</li>
                <li>Assurez-vous que le texte est lisible</li>
                <li>Essayez avec une image avec plus de contraste</li>
            </ul>

            <h3>Performance lente</h3>
            <ul>
                <li>Réduisez la taille des images très grandes</li>
                <li>Utilisez des images avec un bon contraste</li>
                <li>Le prétraitement améliore la précision mais prend plus de temps</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2><i class="fas fa-heart"></i> À propos</h2>
            <p>Cette application utilise :</p>
            <ul>
                <li><strong>Flask</strong> - Framework web Python</li>
                <li><strong>Tesseract OCR</strong> - Moteur de reconnaissance optique de caractères</li>
                <li><strong>OpenCV</strong> - Traitement d'image</li>
                <li><strong>HTML5/CSS3/JavaScript</strong> - Interface utilisateur moderne</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 40px;">
            <a href="http://localhost:5000" class="btn">
                <i class="fas fa-play"></i> Lancer l'application
            </a>
            <a href="install_tesseract.md" class="btn">
                <i class="fas fa-download"></i> Installer Tesseract
            </a>
        </div>
    </div>
</body>
</html>
