#!/bin/bash

echo "========================================"
echo " Convertisseur Image vers Texte"
echo "========================================"
echo

echo "Vérification des dépendances..."
python3 -c "import flask, cv2, pytesseract, PIL, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "ERREUR: Dépendances manquantes!"
    echo "Installation des dépendances..."
    pip3 install Flask opencv-python pytesseract Pillow numpy
    if [ $? -ne 0 ]; then
        echo "ERREUR: Impossible d'installer les dépendances"
        exit 1
    fi
fi

echo "Dépendances OK!"
echo

echo "Vérification de Tesseract OCR..."
tesseract --version >/dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "ATTENTION: Tesseract OCR n'est pas installé ou pas dans le PATH"
    echo "L'application fonctionnera mais l'OCR ne sera pas disponible"
    echo "Consultez install_tesseract.md pour les instructions d'installation"
    echo
    echo "Installation automatique (Ubuntu/Debian):"
    echo "sudo apt install tesseract-ocr tesseract-ocr-fra"
    echo
    echo "Installation automatique (macOS):"
    echo "brew install tesseract"
    echo
    read -p "Appuyez sur Entrée pour continuer quand même..."
else
    echo "Tesseract OCR détecté!"
fi

echo
echo "Démarrage de l'application..."
echo "L'application sera accessible sur: http://localhost:5000"
echo
echo "Appuyez sur Ctrl+C pour arrêter l'application"
echo

python3 app.py
