@echo off
echo ========================================
echo  Installation de Tesseract OCR
echo ========================================
echo.

echo Verification si Tesseract est deja installe...
tesseract --version >nul 2>&1
if not errorlevel 1 (
    echo Tesseract est deja installe!
    tesseract --version
    echo.
    echo Appuyez sur une touche pour continuer...
    pause >nul
    exit /b 0
)

echo Tesseract n'est pas detecte dans le PATH.
echo.

echo Verification des chemins d'installation standards...
set "TESSERACT_FOUND="

if exist "C:\Program Files\Tesseract-OCR\tesseract.exe" (
    echo Trouve: C:\Program Files\Tesseract-OCR\tesseract.exe
    set "TESSERACT_FOUND=C:\Program Files\Tesseract-OCR"
)

if exist "C:\Program Files (x86)\Tesseract-OCR\tesseract.exe" (
    echo Trouve: C:\Program Files (x86)\Tesseract-OCR\tesseract.exe
    set "TESSERACT_FOUND=C:\Program Files (x86)\Tesseract-OCR"
)

if defined TESSERACT_FOUND (
    echo.
    echo Tesseract est installe mais pas dans le PATH.
    echo Ajout au PATH pour cette session...
    set "PATH=%TESSERACT_FOUND%;%PATH%"
    
    echo Test de Tesseract...
    tesseract --version
    if not errorlevel 1 (
        echo.
        echo SUCCESS! Tesseract fonctionne maintenant.
        echo.
        echo IMPORTANT: Pour rendre ce changement permanent:
        echo 1. Ouvrez "Variables d'environnement systeme"
        echo 2. Ajoutez "%TESSERACT_FOUND%" au PATH
        echo 3. Redemarrez votre terminal
        echo.
        echo Ou relancez ce script a chaque fois.
        echo.
        echo Appuyez sur une touche pour continuer...
        pause >nul
        exit /b 0
    )
)

echo.
echo Tesseract OCR n'est pas installe sur ce systeme.
echo.
echo Options d'installation:
echo.
echo 1. Installation automatique (recommandee)
echo 2. Installation manuelle
echo 3. Annuler
echo.
set /p choice="Votre choix (1-3): "

if "%choice%"=="1" goto auto_install
if "%choice%"=="2" goto manual_install
if "%choice%"=="3" goto cancel
goto invalid_choice

:auto_install
echo.
echo Tentative d'installation automatique...
echo.

REM Verifier si winget est disponible
winget --version >nul 2>&1
if not errorlevel 1 (
    echo Installation via winget...
    winget install --id UB-Mannheim.TesseractOCR
    if not errorlevel 1 (
        echo.
        echo Installation terminee! Redemarrage du terminal requis.
        echo Fermez ce terminal et relancez l'application.
        pause
        exit /b 0
    )
)

REM Verifier si chocolatey est disponible
choco --version >nul 2>&1
if not errorlevel 1 (
    echo Installation via Chocolatey...
    choco install tesseract
    if not errorlevel 1 (
        echo.
        echo Installation terminee! Redemarrage du terminal requis.
        echo Fermez ce terminal et relancez l'application.
        pause
        exit /b 0
    )
)

echo.
echo Installation automatique non disponible.
echo Passage a l'installation manuelle...
goto manual_install

:manual_install
echo.
echo Installation manuelle:
echo.
echo 1. Ouvrez votre navigateur
echo 2. Allez sur: https://github.com/UB-Mannheim/tesseract/wiki
echo 3. Telechargez la version 64-bit la plus recente
echo 4. Executez l'installateur en tant qu'administrateur
echo 5. IMPORTANT: Cochez "Add to PATH" pendant l'installation
echo 6. Redemarrez votre terminal
echo.
echo Ouverture du navigateur...
start https://github.com/UB-Mannheim/tesseract/wiki
echo.
echo Appuyez sur une touche une fois l'installation terminee...
pause >nul
goto test_installation

:test_installation
echo.
echo Test de l'installation...
tesseract --version >nul 2>&1
if not errorlevel 1 (
    echo.
    echo SUCCESS! Tesseract est maintenant installe et configure.
    tesseract --version
    echo.
    echo Vous pouvez maintenant utiliser l'application OCR.
) else (
    echo.
    echo Tesseract n'est toujours pas detecte.
    echo Verifiez que:
    echo 1. L'installation s'est bien deroulee
    echo 2. Vous avez coche "Add to PATH"
    echo 3. Vous avez redémarre votre terminal
    echo.
    echo Si le probleme persiste, modifiez app.py avec le chemin complet.
)
echo.
echo Appuyez sur une touche pour continuer...
pause >nul
exit /b 0

:invalid_choice
echo Choix invalide. Veuillez entrer 1, 2 ou 3.
goto auto_install

:cancel
echo Installation annulee.
echo.
echo L'application fonctionnera sans OCR jusqu'a l'installation de Tesseract.
echo Consultez install_tesseract.md pour plus d'informations.
pause
exit /b 1
