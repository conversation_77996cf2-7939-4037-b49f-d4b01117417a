from flask import Flask, render_template, request, jsonify
import os
from werkzeug.utils import secure_filename
from image_to_text import ImageToTextConverter
import logging

# Configuration de l'application Flask
app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configuration des dossiers
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'tif'}

# Créer le dossier uploads s'il n'existe pas
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Initialiser le convertisseur OCR
try:
    # Essayer d'abord avec le chemin par défaut
    converter = ImageToTextConverter(lang='fra+eng')
    logging.info("Convertisseur OCR initialisé avec succès")
except Exception as e:
    # Essayer avec le chemin d'installation standard de Windows
    try:
        tesseract_paths = [
            r'C:\Program Files\Tesseract-OCR\tesseract.exe',
            r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
            r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe'.format(os.environ.get('USERNAME', '')),
        ]

        converter = None
        for path in tesseract_paths:
            if os.path.exists(path):
                converter = ImageToTextConverter(tesseract_cmd=path, lang='fra+eng')
                logging.info(f"Convertisseur OCR initialisé avec le chemin: {path}")
                break

        if converter is None:
            raise Exception("Tesseract non trouvé dans les chemins standards")

    except Exception as e2:
        logging.error(f"Erreur lors de l'initialisation du convertisseur OCR: {e}")
        logging.error(f"Tentative avec chemins standards échouée: {e2}")
        logging.error("Assurez-vous que Tesseract OCR est installé. Voir install_tesseract.md pour les instructions.")
        converter = None

def allowed_file(filename):
    """Vérifie si le fichier a une extension autorisée."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    """Page d'accueil avec le formulaire de téléchargement."""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Traite le fichier téléchargé et extrait le texte."""
    if 'file' not in request.files:
        return jsonify({'error': 'Aucun fichier sélectionné'}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({'error': 'Aucun fichier sélectionné'}), 400

    if not allowed_file(file.filename):
        return jsonify({'error': 'Type de fichier non autorisé. Utilisez: PNG, JPG, JPEG, GIF, BMP, TIFF'}), 400

    if converter is None:
        return jsonify({
            'error': 'Service OCR non disponible. Tesseract OCR n\'est pas installé ou configuré.',
            'help': 'Consultez le fichier install_tesseract.md pour les instructions d\'installation.'
        }), 500

    try:
        # Sauvegarder le fichier temporairement
        filename = secure_filename(file.filename)
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        file.save(filepath)

        # Extraire le texte de l'image
        extracted_text = converter.extract_text(filepath, preprocess=True)

        # Supprimer le fichier temporaire
        os.remove(filepath)

        if not extracted_text.strip():
            return jsonify({'warning': 'Aucun texte détecté dans l\'image', 'text': ''}), 200

        return jsonify({'success': True, 'text': extracted_text}), 200

    except Exception as e:
        # Nettoyer le fichier en cas d'erreur
        if os.path.exists(filepath):
            os.remove(filepath)

        logging.error(f"Erreur lors du traitement de l'image: {e}")
        return jsonify({'error': f'Erreur lors du traitement: {str(e)}'}), 500

@app.route('/health')
def health_check():
    """Endpoint pour vérifier l'état du service."""
    status = {
        'status': 'ok',
        'ocr_available': converter is not None
    }

    if converter:
        try:
            # Test simple pour vérifier que Tesseract fonctionne
            import pytesseract
            version = pytesseract.get_tesseract_version()
            status['tesseract_version'] = str(version)
        except Exception as e:
            status['ocr_available'] = False
            status['error'] = str(e)

    return jsonify(status)

if __name__ == '__main__':
    # Créer les dossiers nécessaires
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)

    # Lancer l'application
    app.run(debug=True, host='0.0.0.0', port=5000)
